// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('群聊功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问应用首页
    await page.goto('/');
    
    // 模拟登录（如果需要）
    // 这里可以添加登录逻辑
  });

  test('应该能显示创建群聊按钮', async ({ page }) => {
    // 导航到聊天页面
    await page.click('[data-tab="chat"]');
    
    // 检查群聊按钮是否存在
    const groupChatBtn = page.locator('#group-chat-btn');
    await expect(groupChatBtn).toBeVisible();
    
    // 检查按钮样式
    await expect(groupChatBtn).toHaveCSS('background-color', 'rgb(7, 193, 96)');
  });

  test('应该能点击群聊按钮打开创建群聊页面', async ({ page }) => {
    // 导航到聊天页面
    await page.click('[data-tab="chat"]');
    
    // 点击群聊按钮
    await page.click('#group-chat-btn');
    
    // 检查创建群聊页面是否显示
    const createGroupPage = page.locator('#create-group-chat');
    await expect(createGroupPage).toHaveClass(/active/);
    
    // 检查页面标题
    await expect(page.locator('#create-group-chat h3')).toHaveText('创建群聊');
  });

  test('应该显示群聊创建表单元素', async ({ page }) => {
    // 导航到聊天页面并打开创建群聊
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 检查表单元素
    await expect(page.locator('#group-name-input')).toBeVisible();
    await expect(page.locator('#group-avatar-preview')).toBeVisible();
    await expect(page.locator('#change-avatar-btn')).toBeVisible();
    await expect(page.locator('#create-group-confirm')).toBeVisible();
    
    // 检查成员选择区域
    await expect(page.locator('#selected-count')).toHaveText('已选择 0 人');
    await expect(page.locator('#member-search')).toBeVisible();
  });

  test('应该能输入群聊名称并显示字符计数', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 输入群聊名称
    const groupNameInput = page.locator('#group-name-input');
    await groupNameInput.fill('测试群聊');
    
    // 检查字符计数
    await expect(page.locator('.input-counter')).toHaveText('4/20');
    
    // 测试最大长度限制
    await groupNameInput.fill('这是一个很长的群聊名称用来测试最大长度限制功能');
    await expect(page.locator('.input-counter')).toHaveText('20/20');
  });

  test('应该能搜索和选择群聊成员', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 等待好友列表加载
    await page.waitForSelector('#create-group-friends-list .friend-item', { timeout: 10000 });
    
    // 检查是否有好友列表
    const friendItems = page.locator('#create-group-friends-list .friend-item');
    const friendCount = await friendItems.count();
    
    if (friendCount > 0) {
      // 点击第一个好友
      await friendItems.first().click();
      
      // 检查是否被选中
      await expect(friendItems.first()).toHaveClass(/selected/);
      
      // 检查已选择计数更新
      await expect(page.locator('#selected-count')).toHaveText('已选择 1 人');
      
      // 检查已选择成员显示
      const selectedMembers = page.locator('#selected-members .selected-member');
      await expect(selectedMembers).toHaveCount(1);
    }
  });

  test('应该验证群聊创建条件', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 检查完成按钮初始状态（应该是禁用的）
    const confirmBtn = page.locator('#create-group-confirm');
    await expect(confirmBtn).toBeDisabled();
    
    // 输入群聊名称
    await page.locator('#group-name-input').fill('测试群聊');
    
    // 仍然应该是禁用的（因为没有选择成员）
    await expect(confirmBtn).toBeDisabled();
    
    // 等待好友列表加载并选择足够的成员
    await page.waitForSelector('#create-group-friends-list .friend-item', { timeout: 10000 });
    
    const friendItems = page.locator('#create-group-friends-list .friend-item');
    const friendCount = await friendItems.count();
    
    if (friendCount >= 2) {
      // 选择前两个好友
      await friendItems.nth(0).click();
      await friendItems.nth(1).click();
      
      // 现在完成按钮应该被启用
      await expect(confirmBtn).toBeEnabled();
    }
  });

  test('应该能返回到聊天列表', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 点击返回按钮
    await page.click('#back-to-chat-list');
    
    // 检查创建群聊页面是否关闭
    const createGroupPage = page.locator('#create-group-chat');
    await expect(createGroupPage).not.toHaveClass(/active/);
  });

  test('应该能处理搜索功能', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 等待好友列表加载
    await page.waitForSelector('#create-group-friends-list .friend-item', { timeout: 10000 });
    
    const searchInput = page.locator('#member-search');
    const friendItems = page.locator('#create-group-friends-list .friend-item');
    
    const initialCount = await friendItems.count();
    
    if (initialCount > 0) {
      // 输入搜索关键词
      await searchInput.fill('张');
      
      // 等待搜索结果
      await page.waitForTimeout(500);
      
      // 检查过滤后的结果
      const visibleItems = friendItems.locator(':visible');
      const visibleCount = await visibleItems.count();
      
      // 搜索后的结果应该不多于原始结果
      expect(visibleCount).toBeLessThanOrEqual(initialCount);
    }
  });

  test('移动端设备应该有响应式设计', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 检查移动端适配
    const groupChatBtn = page.locator('#group-chat-btn');
    const buttonStyle = await groupChatBtn.evaluate(el => {
      const style = window.getComputedStyle(el);
      return {
        width: style.width,
        height: style.height
      };
    });
    
    // 在移动端，按钮应该更小
    expect(parseInt(buttonStyle.width)).toBeLessThanOrEqual(40);
    expect(parseInt(buttonStyle.height)).toBeLessThanOrEqual(40);
  });

  test('应该能处理空的好友列表', async ({ page }) => {
    // 打开创建群聊页面
    await page.click('[data-tab="chat"]');
    await page.click('#group-chat-btn');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
    
    // 检查是否显示"暂无好友可选择"消息或有好友列表
    const friendsList = page.locator('#create-group-friends-list');
    const noFriendsMessage = page.locator('.no-friends-message');
    const friendItems = page.locator('#create-group-friends-list .friend-item');
    
    const friendCount = await friendItems.count();
    
    if (friendCount === 0) {
      // 如果没有好友，应该显示提示消息
      await expect(noFriendsMessage).toBeVisible();
    } else {
      // 如果有好友，应该不显示提示消息
      await expect(noFriendsMessage).not.toBeVisible();
    }
  });
});